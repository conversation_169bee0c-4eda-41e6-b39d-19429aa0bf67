import { useFloatingElement, type FloatingElement } from '@/contexts/floating-ui-context';
import { useEffect } from 'react';

// Simplified hook for common floating UI patterns
export function useFloatingButton(
	id: string,
	type: FloatingElement['type'] = 'custom',
	options?: {
		priority?: number;
		isVisible?: boolean;
		position?: { bottom: number; right: number };
	}
) {
	const { position, setVisible, setPosition, setPriority } = useFloatingElement(id, type, options);

	return {
		position,
		setVisible,
		setPosition,
		setPriority,
		// Helper to get inline styles for the element
		getStyles: () => ({
			position: 'fixed' as const,
			bottom: `${position?.bottom || 24}px`,
			right: `${position?.right || 24}px`,
			zIndex: position?.zIndex || 50,
		}),
	};
}

// Hook for page guidance specifically
export function usePageGuidance(
	id: string,
	options?: {
		defaultOpen?: boolean;
		priority?: number;
	}
) {
	const { position, setVisible } = useFloatingElement(id, 'page-guidance', {
		priority: options?.priority || 1,
		isVisible: !options?.defaultOpen, // Float button visible when guidance is closed
	});

	const toggleGuidance = (isOpen: boolean) => {
		setVisible(!isOpen); // Float button visible when guidance is closed
	};

	return {
		position,
		toggleGuidance,
		getFloatButtonStyles: () => ({
			position: 'fixed' as const,
			bottom: `${position?.bottom || 16}px`,
			right: `${position?.right || 16}px`,
			zIndex: position?.zIndex || 50,
		}),
	};
}

// Hook for settings/menu buttons
export function useSettingsButton(id: string = 'settings-button') {
	return useFloatingButton(id, 'settings', {
		priority: 10, // High priority for settings
	});
}

// Hook for info buttons
export function useInfoButton(id: string) {
	return useFloatingButton(id, 'info', {
		priority: 5,
	});
}

// Hook for action buttons (like add, create, etc.)
export function useActionButton(id: string) {
	return useFloatingButton(id, 'float-button', {
		priority: 3,
	});
}
