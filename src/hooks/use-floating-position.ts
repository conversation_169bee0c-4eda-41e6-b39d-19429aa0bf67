import { useMemo } from 'react';

// Simple floating position management without complex context
export type FloatingType = 'page-guidance' | 'float-button' | 'settings' | 'info' | 'custom';

interface FloatingConfig {
	priority: number;
	defaultPosition: { bottom: number; right: number };
	zIndex: number;
}

const FLOATING_CONFIGS: Record<FloatingType, FloatingConfig> = {
	'page-guidance': { priority: 1, defaultPosition: { bottom: 16, right: 16 }, zIndex: 50 },
	'float-button': { priority: 2, defaultPosition: { bottom: 24, right: 24 }, zIndex: 51 },
	'settings': { priority: 10, defaultPosition: { bottom: 24, right: 24 }, zIndex: 60 }, // Highest priority
	'info': { priority: 5, defaultPosition: { bottom: 96, right: 24 }, zIndex: 55 },
	'custom': { priority: 3, defaultPosition: { bottom: 24, right: 24 }, zIndex: 52 },
};

export function useFloatingPosition(
	type: FloatingType,
	options?: {
		position?: { bottom: number; right: number };
		zIndex?: number;
	}
) {
	const config = FLOATING_CONFIGS[type];
	
	const styles = useMemo(() => ({
		position: 'fixed' as const,
		bottom: `${options?.position?.bottom || config.defaultPosition.bottom}px`,
		right: `${options?.position?.right || config.defaultPosition.right}px`,
		zIndex: options?.zIndex || config.zIndex,
	}), [config, options?.position?.bottom, options?.position?.right, options?.zIndex]);

	return {
		styles,
		priority: config.priority,
		zIndex: options?.zIndex || config.zIndex,
	};
}

// Specific hooks for common use cases
export function usePageGuidancePosition(options?: { position?: { bottom: number; right: number } }) {
	return useFloatingPosition('page-guidance', options);
}

export function useSettingsButtonPosition(options?: { position?: { bottom: number; right: number } }) {
	return useFloatingPosition('settings', options);
}

export function useInfoButtonPosition(options?: { position?: { bottom: number; right: number } }) {
	return useFloatingPosition('info', options);
}

export function useFloatButtonPosition(options?: { position?: { bottom: number; right: number } }) {
	return useFloatingPosition('float-button', options);
}
