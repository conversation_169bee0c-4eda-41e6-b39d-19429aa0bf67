'use client';

import { <PERSON><PERSON>, <PERSON>, CardContent, Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { 
	ArrowR<PERSON>, 
	BookOpen, 
	Plus,
	Target
} from 'lucide-react';
import Link from 'next/link';

interface EmptyStateGuidanceProps {
	titleKey: string;
	descriptionKey: string;
	actionKey: string;
	actionHref: string;
	icon?: React.ComponentType<any>;
	className?: string;
}

export function EmptyStateGuidance({ 
	titleKey, 
	descriptionKey, 
	actionKey,
	actionHref,
	icon: Icon = BookOpen,
	className = ''
}: EmptyStateGuidanceProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.5 }}
			className={className}
		>
			<Card className="border-dashed border-2 border-muted-foreground/20 bg-muted/10">
				<CardContent className="flex flex-col items-center justify-center text-center p-12">
					{/* Icon */}
					<motion.div
						initial={{ scale: 0.8 }}
						animate={{ scale: 1 }}
						transition={{ delay: 0.2, duration: 0.3 }}
						className="mb-6"
					>
						<div className="p-4 rounded-full bg-muted/50">
							<Icon className="h-12 w-12 text-muted-foreground" />
						</div>
					</motion.div>

					{/* Content */}
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ delay: 0.4, duration: 0.3 }}
						className="space-y-4 max-w-md"
					>
						<h3 className="text-xl font-semibold text-foreground">
							<Translate text={titleKey} />
						</h3>
						<p className="text-muted-foreground leading-relaxed">
							<Translate text={descriptionKey} />
						</p>
					</motion.div>

					{/* Action */}
					<motion.div
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.6, duration: 0.3 }}
						className="mt-8"
					>
						<Link href={actionHref}>
							<Button className="flex items-center gap-2">
								<Plus className="h-4 w-4" />
								<Translate text={actionKey} />
								<ArrowRight className="h-4 w-4" />
							</Button>
						</Link>
					</motion.div>
				</CardContent>
			</Card>
		</motion.div>
	);
}
