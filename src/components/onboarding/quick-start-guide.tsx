'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { motion } from 'framer-motion';
import { 
	BookO<PERSON>, 
	CheckCircle2, 
	Circle,
	Zap,
	RefreshCw,
	TrendingUp,
	ArrowRight,
	Play
} from 'lucide-react';
import Link from 'next/link';

interface QuickStartStep {
	id: string;
	titleKey: string;
	descriptionKey: string;
	icon: React.ComponentType<any>;
	href: string;
	completed: boolean;
	disabled: boolean;
	disabledReason?: string;
}

interface QuickStartGuideProps {
	collectionId: string;
	wordCount: number;
	hasReviewed: boolean;
	hasCompletedQuiz: boolean;
}

export function QuickStartGuide({ 
	collectionId, 
	wordCount, 
	hasReviewed = false,
	hasCompletedQuiz = false 
}: QuickStartGuideProps) {
	const { t } = useTranslation();

	const steps: QuickStartStep[] = [
		{
			id: 'add-words',
			titleKey: 'collections.quick_start.step1_title',
			descriptionKey: 'collections.quick_start.step1_desc',
			icon: Zap,
			href: `/collections/${collectionId}/vocabulary/generate`,
			completed: wordCount > 0,
			disabled: false,
		},
		{
			id: 'review-practice',
			titleKey: 'collections.quick_start.step2_title',
			descriptionKey: 'collections.quick_start.step2_desc',
			icon: RefreshCw,
			href: `/collections/${collectionId}/vocabulary/review`,
			completed: hasReviewed,
			disabled: wordCount === 0,
			disabledReason: t('collections.overview.review_disabled_reason'),
		},
		{
			id: 'track-progress',
			titleKey: 'collections.quick_start.step3_title',
			descriptionKey: 'collections.quick_start.step3_desc',
			icon: TrendingUp,
			href: `/collections/${collectionId}/stats`,
			completed: hasCompletedQuiz,
			disabled: false,
		},
	];

	const completedSteps = steps.filter(step => step.completed).length;
	const progressPercentage = (completedSteps / steps.length) * 100;

	return (
		<Card className="border-border/50 bg-gradient-to-br from-background to-muted/20">
			<CardHeader className="pb-4">
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-3">
						<div className="p-2 rounded-lg bg-primary/10">
							<Play className="h-5 w-5 text-primary" />
						</div>
						<Translate text="collections.quick_start.title" />
					</CardTitle>
					<div className="text-sm text-muted-foreground">
						{completedSteps}/{steps.length} completed
					</div>
				</div>
				
				{/* Progress bar */}
				<div className="w-full bg-muted rounded-full h-2 mt-4">
					<motion.div
						className="bg-primary h-2 rounded-full"
						initial={{ width: 0 }}
						animate={{ width: `${progressPercentage}%` }}
						transition={{ duration: 0.5, ease: "easeOut" }}
					/>
				</div>
			</CardHeader>

			<CardContent className="space-y-4">
				{steps.map((step, index) => (
					<motion.div
						key={step.id}
						initial={{ opacity: 0, x: -20 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ delay: index * 0.1 }}
					>
						<StepCard
							step={step}
							stepNumber={index + 1}
						/>
					</motion.div>
				))}
			</CardContent>
		</Card>
	);
}

interface StepCardProps {
	step: QuickStartStep;
	stepNumber: number;
}

function StepCard({ step, stepNumber }: StepCardProps) {
	const { t } = useTranslation();
	const Icon = step.icon;

	const content = (
		<div className={`
			flex items-center gap-4 p-4 rounded-lg border transition-all duration-200
			${step.completed 
				? 'bg-primary/5 border-primary/20 hover:bg-primary/10' 
				: step.disabled 
					? 'bg-muted/30 border-muted cursor-not-allowed opacity-60'
					: 'bg-background border-border hover:border-primary/30 hover:shadow-md'
			}
		`}>
			{/* Step indicator */}
			<div className="flex-shrink-0">
				{step.completed ? (
					<div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
						<CheckCircle2 className="h-5 w-5 text-primary-foreground" />
					</div>
				) : (
					<div className={`
						w-8 h-8 rounded-full border-2 flex items-center justify-center
						${step.disabled 
							? 'border-muted-foreground/30 text-muted-foreground/30' 
							: 'border-primary text-primary'
						}
					`}>
						<span className="text-sm font-medium">{stepNumber}</span>
					</div>
				)}
			</div>

			{/* Icon */}
			<div className={`
				p-2 rounded-lg
				${step.completed 
					? 'bg-primary/10' 
					: step.disabled 
						? 'bg-muted/50' 
						: 'bg-muted/70'
				}
			`}>
				<Icon className={`
					h-5 w-5
					${step.completed 
						? 'text-primary' 
						: step.disabled 
							? 'text-muted-foreground/50' 
							: 'text-muted-foreground'
					}
				`} />
			</div>

			{/* Content */}
			<div className="flex-1">
				<h4 className={`
					font-medium mb-1
					${step.disabled ? 'text-muted-foreground/70' : 'text-foreground'}
				`}>
					<Translate text={step.titleKey} />
				</h4>
				<p className={`
					text-sm
					${step.disabled ? 'text-muted-foreground/50' : 'text-muted-foreground'}
				`}>
					<Translate text={step.descriptionKey} />
				</p>
				{step.disabled && step.disabledReason && (
					<p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
						{step.disabledReason}
					</p>
				)}
			</div>

			{/* Action arrow */}
			{!step.disabled && (
				<ArrowRight className={`
					h-5 w-5 transition-transform duration-200 group-hover:translate-x-1
					${step.completed ? 'text-primary' : 'text-muted-foreground'}
				`} />
			)}
		</div>
	);

	if (step.disabled) {
		return content;
	}

	return (
		<Link href={step.href} className="block group">
			{content}
		</Link>
	);
}
