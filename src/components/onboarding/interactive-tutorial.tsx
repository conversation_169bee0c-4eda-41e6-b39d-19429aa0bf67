'use client';

import { <PERSON><PERSON>, <PERSON>, CardContent, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { motion, AnimatePresence } from 'framer-motion';
import { 
	X, 
	ArrowLeft, 
	ArrowRight, 
	BookOpen, 
	FileText, 
	TrendingUp,
	Lightbulb,
	Target
} from 'lucide-react';
import { useState, useEffect, useRef } from 'react';

interface TutorialStep {
	id: string;
	titleKey: string;
	contentKey: string;
	targetSelector: string;
	position: 'top' | 'bottom' | 'left' | 'right';
	highlightOffset?: { x: number; y: number };
}

interface InteractiveTutorialProps {
	isOpen: boolean;
	onClose: () => void;
	onComplete: () => void;
}

export function InteractiveTutorial({ isOpen, onClose, onComplete }: InteractiveTutorialProps) {
	const { t } = useTranslation();
	const [currentStep, setCurrentStep] = useState(0);
	const [highlightPosition, setHighlightPosition] = useState({ x: 0, y: 0, width: 0, height: 0 });
	const tooltipRef = useRef<HTMLDivElement>(null);

	const steps: TutorialStep[] = [
		{
			id: 'vocabulary-section',
			titleKey: 'collections.tabs.vocabulary',
			contentKey: 'collections.tooltip.vocabulary_section',
			targetSelector: '[data-tutorial="vocabulary-section"]',
			position: 'bottom',
		},
		{
			id: 'paragraph-section',
			titleKey: 'collections.tabs.paragraphs',
			contentKey: 'collections.tooltip.paragraph_section',
			targetSelector: '[data-tutorial="paragraph-section"]',
			position: 'bottom',
		},
		{
			id: 'stats-section',
			titleKey: 'collections.stats.title',
			contentKey: 'collections.tooltip.stats_section',
			targetSelector: '[data-tutorial="stats-section"]',
			position: 'top',
		},
	];

	const currentStepData = steps[currentStep];

	useEffect(() => {
		if (!isOpen || !currentStepData) return;

		const updateHighlight = () => {
			const target = document.querySelector(currentStepData.targetSelector);
			if (target) {
				const rect = target.getBoundingClientRect();
				setHighlightPosition({
					x: rect.left,
					y: rect.top,
					width: rect.width,
					height: rect.height,
				});
			}
		};

		updateHighlight();
		window.addEventListener('resize', updateHighlight);
		window.addEventListener('scroll', updateHighlight);

		return () => {
			window.removeEventListener('resize', updateHighlight);
			window.removeEventListener('scroll', updateHighlight);
		};
	}, [currentStep, currentStepData, isOpen]);

	const handleNext = () => {
		if (currentStep < steps.length - 1) {
			setCurrentStep(currentStep + 1);
		} else {
			handleComplete();
		}
	};

	const handlePrevious = () => {
		if (currentStep > 0) {
			setCurrentStep(currentStep - 1);
		}
	};

	const handleComplete = () => {
		onComplete();
		onClose();
	};

	const handleSkip = () => {
		onClose();
	};

	if (!isOpen || !currentStepData) return null;

	return (
		<>
			{/* Overlay */}
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				exit={{ opacity: 0 }}
				className="fixed inset-0 bg-black/50 z-40"
				onClick={onClose}
			/>

			{/* Highlight */}
			<motion.div
				className="fixed z-50 pointer-events-none"
				initial={{ opacity: 0, scale: 0.8 }}
				animate={{ 
					opacity: 1, 
					scale: 1,
					x: highlightPosition.x - 8,
					y: highlightPosition.y - 8,
					width: highlightPosition.width + 16,
					height: highlightPosition.height + 16,
				}}
				transition={{ duration: 0.3 }}
				style={{
					boxShadow: '0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 9999px rgba(0, 0, 0, 0.5)',
					borderRadius: '12px',
				}}
			/>

			{/* Tooltip */}
			<AnimatePresence mode="wait">
				<motion.div
					key={currentStep}
					ref={tooltipRef}
					initial={{ opacity: 0, scale: 0.9, y: 20 }}
					animate={{ opacity: 1, scale: 1, y: 0 }}
					exit={{ opacity: 0, scale: 0.9, y: -20 }}
					transition={{ duration: 0.3 }}
					className="fixed z-50"
					style={getTooltipPosition(highlightPosition, currentStepData.position)}
				>
					<Card className="w-80 border-primary/20 bg-background shadow-2xl">
						<CardContent className="p-6">
							{/* Header */}
							<div className="flex items-start justify-between mb-4">
								<div className="flex items-center gap-3">
									<div className="p-2 rounded-lg bg-primary/10">
										<Lightbulb className="h-5 w-5 text-primary" />
									</div>
									<div>
										<h3 className="font-semibold text-lg">
											<Translate text={currentStepData.titleKey} />
										</h3>
										<p className="text-sm text-muted-foreground">
											Step {currentStep + 1} of {steps.length}
										</p>
									</div>
								</div>
								<Button
									variant="ghost"
									size="sm"
									onClick={onClose}
									className="h-8 w-8 p-0"
								>
									<X className="h-4 w-4" />
								</Button>
							</div>

							{/* Content */}
							<p className="text-muted-foreground mb-6 leading-relaxed">
								<Translate text={currentStepData.contentKey} />
							</p>

							{/* Progress indicator */}
							<div className="flex gap-2 mb-6">
								{steps.map((_, index) => (
									<div
										key={index}
										className={`
											h-2 rounded-full transition-all duration-300
											${index === currentStep 
												? 'bg-primary flex-1' 
												: index < currentStep 
													? 'bg-primary/60 w-2' 
													: 'bg-muted w-2'
											}
										`}
									/>
								))}
							</div>

							{/* Actions */}
							<div className="flex items-center justify-between">
								<Button
									variant="ghost"
									size="sm"
									onClick={handleSkip}
									className="text-muted-foreground hover:text-foreground"
								>
									<Translate text="collections.tutorial.skip" />
								</Button>

								<div className="flex gap-2">
									{currentStep > 0 && (
										<Button
											variant="outline"
											size="sm"
											onClick={handlePrevious}
										>
											<ArrowLeft className="h-4 w-4 mr-2" />
											<Translate text="collections.tutorial.previous" />
										</Button>
									)}
									<Button
										size="sm"
										onClick={handleNext}
										className="bg-primary hover:bg-primary/90"
									>
										{currentStep === steps.length - 1 ? (
											<>
												<Translate text="collections.tutorial.finish" />
												<Target className="h-4 w-4 ml-2" />
											</>
										) : (
											<>
												<Translate text="collections.tutorial.next" />
												<ArrowRight className="h-4 w-4 ml-2" />
											</>
										)}
									</Button>
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Tooltip arrow */}
					<div
						className={`
							absolute w-3 h-3 bg-background border-l border-t border-primary/20 rotate-45
							${getArrowPosition(currentStepData.position)}
						`}
					/>
				</motion.div>
			</AnimatePresence>
		</>
	);
}

function getTooltipPosition(
	highlight: { x: number; y: number; width: number; height: number },
	position: 'top' | 'bottom' | 'left' | 'right'
) {
	const offset = 20;
	
	switch (position) {
		case 'top':
			return {
				left: highlight.x + highlight.width / 2 - 160, // 160 = half of tooltip width (320px)
				top: highlight.y - offset - 200, // Approximate tooltip height
			};
		case 'bottom':
			return {
				left: highlight.x + highlight.width / 2 - 160,
				top: highlight.y + highlight.height + offset,
			};
		case 'left':
			return {
				left: highlight.x - 320 - offset,
				top: highlight.y + highlight.height / 2 - 100,
			};
		case 'right':
			return {
				left: highlight.x + highlight.width + offset,
				top: highlight.y + highlight.height / 2 - 100,
			};
		default:
			return { left: 0, top: 0 };
	}
}

function getArrowPosition(position: 'top' | 'bottom' | 'left' | 'right') {
	switch (position) {
		case 'top':
			return 'bottom-[-6px] left-1/2 transform -translate-x-1/2';
		case 'bottom':
			return 'top-[-6px] left-1/2 transform -translate-x-1/2';
		case 'left':
			return 'right-[-6px] top-1/2 transform -translate-y-1/2';
		case 'right':
			return 'left-[-6px] top-1/2 transform -translate-y-1/2';
		default:
			return '';
	}
}
