'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { RefreshCw, Target, Plus, TrendingUp, ArrowRight, Lock } from 'lucide-react';
import Link from 'next/link';

interface NextStep {
	id: string;
	titleKey: string;
	descriptionKey: string;
	icon: React.ComponentType<any>;
	href: string;
	available: boolean;
	primary?: boolean;
	lockReason?: string;
}

interface NextStepsGuideProps {
	collectionId: string;
	wordCount: number;
	className?: string;
}

export function NextStepsGuide({ collectionId, wordCount, className = '' }: NextStepsGuideProps) {
	const canTakeQuiz = wordCount >= 10;
	const wordsNeededForQuiz = Math.max(0, 10 - wordCount);

	const nextSteps: NextStep[] = [
		{
			id: 'review',
			titleKey: 'words.next_steps.review_words',
			descriptionKey: 'words.next_steps.review_description',
			icon: Refresh<PERSON><PERSON>,
			href: `/collections/${collectionId}/vocabulary/review`,
			available: wordCount > 0,
			primary: true,
		},
		{
			id: 'add-more',
			titleKey: 'words.next_steps.add_more_words',
			descriptionKey: 'words.next_steps.add_more_description',
			icon: Plus,
			href: `/collections/${collectionId}/vocabulary/generate`,
			available: true,
		},
		{
			id: 'quiz',
			titleKey: 'words.next_steps.take_quiz',
			descriptionKey: 'words.next_steps.quiz_description',
			icon: Target,
			href: `/collections/${collectionId}/vocabulary/mcq`,
			available: canTakeQuiz,
			lockReason: `Add ${wordsNeededForQuiz} more words to unlock quizzes`,
		},
		{
			id: 'stats',
			titleKey: 'collections.stats.title',
			descriptionKey: 'collections.stats.description',
			icon: TrendingUp,
			href: `/collections/${collectionId}/stats`,
			available: true,
		},
	];

	const availableSteps = nextSteps.filter((step) => step.available);
	const lockedSteps = nextSteps.filter((step) => !step.available);

	if (wordCount === 0) return null;

	return (
		<Card
			className={`border-border/50 bg-gradient-to-br from-background to-muted/20 ${className}`}
		>
			<CardHeader className="pb-4">
				<CardTitle className="flex items-center gap-3">
					<div className="p-2 rounded-lg bg-primary/10">
						<ArrowRight className="h-5 w-5 text-primary" />
					</div>
					<Translate text="words.next_steps.title" />
				</CardTitle>
			</CardHeader>

			<CardContent className="space-y-4">
				{/* Available steps */}
				<div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
					{availableSteps.map((step, index) => (
						<motion.div
							key={step.id}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: index * 0.1 }}
						>
							<Link href={step.href} className="block group">
								<div
									className={`
									p-4 rounded-lg border transition-all duration-200 h-full
									${
										step.primary
											? 'border-primary/30 bg-primary/5 hover:bg-primary/10 hover:border-primary/50'
											: 'border-border hover:border-primary/30 hover:bg-muted/50'
									}
								`}
								>
									<div className="flex items-start gap-3">
										<div
											className={`
											p-2 rounded-lg transition-colors duration-200
											${
												step.primary
													? 'bg-primary/20 text-primary'
													: 'bg-muted text-muted-foreground group-hover:text-primary'
											}
										`}
										>
											<step.icon className="h-5 w-5" />
										</div>
										<div className="flex-1 min-w-0">
											<h4
												className={`
												font-medium mb-1 transition-colors duration-200
												${step.primary ? 'text-primary' : 'text-foreground group-hover:text-primary'}
											`}
											>
												<Translate text={step.titleKey} />
											</h4>
											<p className="text-sm text-muted-foreground leading-relaxed">
												<Translate text={step.descriptionKey} />
											</p>
										</div>
										<ArrowRight
											className={`
											h-4 w-4 transition-all duration-200 group-hover:translate-x-1
											${step.primary ? 'text-primary' : 'text-muted-foreground group-hover:text-primary'}
										`}
										/>
									</div>
								</div>
							</Link>
						</motion.div>
					))}
				</div>

				{/* Locked steps */}
				{lockedSteps.length > 0 && (
					<div className="space-y-3">
						<div className="border-t border-border/50 pt-4">
							<h5 className="text-sm font-medium text-muted-foreground mb-3">
								Coming Soon
							</h5>
							<div className="space-y-2">
								{lockedSteps.map((step, index) => (
									<motion.div
										key={step.id}
										initial={{ opacity: 0, y: 10 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{
											delay: (availableSteps.length + index) * 0.1,
										}}
									>
										<div className="p-3 rounded-lg border border-muted bg-muted/30 opacity-75">
											<div className="flex items-center gap-3">
												<div className="p-2 rounded-lg bg-muted text-muted-foreground">
													<Lock className="h-4 w-4" />
												</div>
												<div className="flex-1">
													<h4 className="font-medium text-muted-foreground mb-1">
														<Translate text={step.titleKey} />
													</h4>
													{step.lockReason && (
														<p className="text-xs text-muted-foreground/70">
															{step.lockReason}
														</p>
													)}
												</div>
											</div>
										</div>
									</motion.div>
								))}
							</div>
						</div>
					</div>
				)}

				{/* Progress indicator */}
				{wordCount < 10 && (
					<div className="mt-4 p-3 rounded-lg bg-orange-50 border border-orange-200 dark:bg-orange-900/20 dark:border-orange-800">
						<div className="flex items-center gap-2 mb-2">
							<Target className="h-4 w-4 text-orange-600 dark:text-orange-400" />
							<span className="text-sm font-medium text-orange-800 dark:text-orange-200">
								Progress to Quiz
							</span>
						</div>
						<div className="w-full bg-orange-200 rounded-full h-2 dark:bg-orange-900/50">
							<div
								className="bg-orange-500 h-2 rounded-full transition-all duration-500"
								style={{ width: `${Math.min((wordCount / 10) * 100, 100)}%` }}
							/>
						</div>
						<p className="text-xs text-orange-700 dark:text-orange-300 mt-1">
							{wordCount}/10 words • {wordsNeededForQuiz} more needed
						</p>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
