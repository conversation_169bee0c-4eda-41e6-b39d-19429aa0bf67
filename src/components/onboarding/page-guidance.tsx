'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Translate } from '@/components/ui';
import { usePageGuidancePosition } from '@/hooks/use-floating-position';
import { motion } from 'framer-motion';
import { HelpCircle, Lightbulb, CheckCircle2, X } from 'lucide-react';
import { useState } from 'react';

interface GuidanceStep {
	key: string;
	icon?: React.ComponentType<any>;
}

interface PageGuidanceProps {
	titleKey: string;
	steps: GuidanceStep[];
	tipKey?: string;
	requirementKey?: string;
	className?: string;
	defaultOpen?: boolean;
	id?: string; // Unique identifier for this guidance instance
}

export function PageGuidance({
	titleKey,
	steps,
	tipKey,
	requirementKey,
	className = '',
	defaultOpen = true,
	id = 'page-guidance',
}: PageGuidanceProps) {
	const [isOpen, setIsOpen] = useState(defaultOpen);

	// Use simple floating position hook
	const { styles } = usePageGuidancePosition();

	if (!isOpen) {
		return (
			<motion.div
				initial={{ opacity: 0, scale: 0.9 }}
				animate={{ opacity: 1, scale: 1 }}
				className={className}
				style={styles}
			>
				<button
					onClick={() => setIsOpen(true)}
					className="p-3 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
				>
					<HelpCircle className="h-6 w-6" />
				</button>
			</motion.div>
		);
	}

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			exit={{ opacity: 0, y: -20 }}
			transition={{ duration: 0.3 }}
			className={className}
		>
			<Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-background">
				<CardHeader className="pb-4">
					<div className="flex items-center justify-between">
						<CardTitle className="flex items-center gap-3">
							<div className="p-2 rounded-lg bg-primary/10">
								<Lightbulb className="h-5 w-5 text-primary" />
							</div>
							<Translate text={titleKey} />
						</CardTitle>
						<button
							onClick={() => setIsOpen(false)}
							className="p-1 rounded-lg hover:bg-muted transition-colors"
						>
							<X className="h-4 w-4 text-muted-foreground" />
						</button>
					</div>
				</CardHeader>
				<CardContent className="space-y-4">
					{/* Steps */}
					<div className="space-y-3">
						{steps.map((step, index) => (
							<motion.div
								key={step.key}
								initial={{ opacity: 0, x: -20 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ delay: index * 0.1 }}
								className="flex items-start gap-3"
							>
								<div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center mt-0.5">
									{step.icon ? (
										<step.icon className="h-3 w-3 text-primary" />
									) : (
										<span className="text-xs font-medium text-primary">
											{index + 1}
										</span>
									)}
								</div>
								<p className="text-sm text-muted-foreground leading-relaxed">
									<Translate text={step.key} />
								</p>
							</motion.div>
						))}
					</div>

					{/* Tip */}
					{tipKey && (
						<motion.div
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: steps.length * 0.1 + 0.2 }}
							className="p-3 rounded-lg bg-amber-50 border border-amber-200 dark:bg-amber-900/20 dark:border-amber-800"
						>
							<div className="flex items-start gap-2">
								<Lightbulb className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
								<p className="text-sm text-amber-800 dark:text-amber-200">
									<Translate text={tipKey} />
								</p>
							</div>
						</motion.div>
					)}

					{/* Requirement */}
					{requirementKey && (
						<motion.div
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: steps.length * 0.1 + 0.3 }}
							className="p-3 rounded-lg bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800"
						>
							<div className="flex items-start gap-2">
								<CheckCircle2 className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
								<p className="text-sm text-blue-800 dark:text-blue-200">
									<Translate text={requirementKey} />
								</p>
							</div>
						</motion.div>
					)}
				</CardContent>
			</Card>
		</motion.div>
	);
}
