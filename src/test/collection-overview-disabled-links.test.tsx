import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { render, screen } from '@testing-library/react';
import { CollectionOverviewClient } from '@/app/collections/[id]/collection-overview-client';
import { CollectionsProvider } from '@/contexts';
import { TranslationProvider } from '@/contexts/translation-context';
import { CollectionWithDetail } from '@/models';
import { useCollections } from '@/hooks';

// Mock the hooks
jest.mock('@/hooks', () => ({
	useCollections: jest.fn(),
}));

// Mock the FeatureCard component to make testing easier
jest.mock('@/components/ui/feature-card', () => ({
	FeatureCard: ({ subFeatures }: { subFeatures: any[] }) => (
		<div data-testid="feature-card">
			{subFeatures?.map((sub, index) => (
				<div
					key={index}
					data-testid={`sub-feature-${sub.titleKey}`}
					data-disabled={sub.disabled}
					data-disabled-reason={sub.disabledReason}
				>
					{sub.titleKey}
				</div>
			))}
		</div>
	),
}));

const mockCollection: CollectionWithDetail = {
	id: 'test-collection-id',
	name: 'Test Collection',
	target_language: 'EN',
	source_language: 'VI',
	user_id: 'test-user-id',
	word_ids: [],
	paragraph_ids: [],
	keyword_ids: [],
	enable_learn_word_notification: false,
	created_at: new Date(),
	updated_at: new Date(),
	words: [],
};

const renderWithProviders = (component: React.ReactElement) => {
	return render(
		<TranslationProvider>
			<CollectionsProvider>{component}</CollectionsProvider>
		</TranslationProvider>
	);
};

describe('CollectionOverviewClient - Disabled Links', () => {
	const mockUseCollections = jest.fn();

	beforeEach(() => {
		jest.clearAllMocks();
		(useCollections as jest.Mock).mockImplementation(mockUseCollections);
	});

	it('should disable review link when collection has no words', () => {
		const collectionWithNoWords = {
			...mockCollection,
			word_ids: [], // No words
		};

		mockUseCollections.mockReturnValue({
			currentCollection: collectionWithNoWords,
			loading: { setCurrent: false, get: false },
			error: null,
			setError: jest.fn(),
		});

		renderWithProviders(<CollectionOverviewClient />);

		const reviewFeature = screen.getByTestId('sub-feature-collections.tabs.review');
		expect(reviewFeature).toHaveAttribute('data-disabled', 'true');
		expect(reviewFeature).toHaveAttribute('data-disabled-reason');
	});

	it('should enable review link when collection has at least 1 word', () => {
		const collectionWithWords = {
			...mockCollection,
			word_ids: ['word1'], // Has 1 word
		};

		mockUseCollections.mockReturnValue({
			currentCollection: collectionWithWords,
			loading: { setCurrent: false, get: false },
			error: null,
			setError: jest.fn(),
		});

		renderWithProviders(<CollectionOverviewClient />);

		const reviewFeature = screen.getByTestId('sub-feature-collections.tabs.review');
		expect(reviewFeature).toHaveAttribute('data-disabled', 'false');
	});

	it('should disable quiz link when collection has less than 10 words', () => {
		const collectionWithFewWords = {
			...mockCollection,
			word_ids: ['word1', 'word2', 'word3'], // Only 3 words
		};

		mockUseCollections.mockReturnValue({
			currentCollection: collectionWithFewWords,
			loading: { setCurrent: false, get: false },
			error: null,
			setError: jest.fn(),
		});

		renderWithProviders(<CollectionOverviewClient />);

		const quizFeature = screen.getByTestId(
			'sub-feature-collections.tabs.multiple_choice_practice'
		);
		expect(quizFeature).toHaveAttribute('data-disabled', 'true');
		expect(quizFeature).toHaveAttribute('data-disabled-reason');
	});

	it('should enable quiz link when collection has at least 10 words', () => {
		const collectionWithEnoughWords = {
			...mockCollection,
			word_ids: Array.from({ length: 10 }, (_, i) => `word${i + 1}`), // 10 words
		};

		mockUseCollections.mockReturnValue({
			currentCollection: collectionWithEnoughWords,
			loading: { setCurrent: false, get: false },
			error: null,
			setError: jest.fn(),
		});

		renderWithProviders(<CollectionOverviewClient />);

		const quizFeature = screen.getByTestId(
			'sub-feature-collections.tabs.multiple_choice_practice'
		);
		expect(quizFeature).toHaveAttribute('data-disabled', 'false');
	});

	it('should handle edge case with exactly 10 words for quiz', () => {
		const collectionWithExactly10Words = {
			...mockCollection,
			word_ids: Array.from({ length: 10 }, (_, i) => `word${i + 1}`), // Exactly 10 words
		};

		mockUseCollections.mockReturnValue({
			currentCollection: collectionWithExactly10Words,
			loading: { setCurrent: false, get: false },
			error: null,
			setError: jest.fn(),
		});

		renderWithProviders(<CollectionOverviewClient />);

		const quizFeature = screen.getByTestId(
			'sub-feature-collections.tabs.multiple_choice_practice'
		);
		expect(quizFeature).toHaveAttribute('data-disabled', 'false');
	});

	it('should show loading skeleton when collection is not loaded', () => {
		mockUseCollections.mockReturnValue({
			currentCollection: null,
			loading: { setCurrent: true, get: false },
			error: null,
			setError: jest.fn(),
		});

		renderWithProviders(<CollectionOverviewClient />);

		// Should not render feature cards when loading
		expect(screen.queryByTestId('feature-card')).not.toBeInTheDocument();
	});
});
