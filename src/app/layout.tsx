import '@/app/globals.css';
import { ClientSettings } from '@/app/components/client-settings';
import { ProgressBar, ThemeProvider, Toaster } from '@/components/ui';
import { TranslationProvider } from '@/contexts/translation-context';
import { AuthProvider } from '@/contexts/auth-context';
import { LoadingProvider } from '@/contexts/loading-context';
import { FloatingUIProvider } from '@/contexts/floating-ui-context';
import { ErrorManagementProvider } from '@/providers/error-management-provider';
import { Metadata } from 'next';
import { Geist, Geist_Mono } from 'next/font/google';

const geist = Geist({
	subsets: ['latin'],
	variable: '--font-geist',
	preload: true,
});

const geistMono = Geist_Mono({
	subsets: ['latin'],
	variable: '--font-geist-mono',
	preload: true,
});

export const metadata: Metadata = {
	title: 'Vocab',
	description: 'Learn new vocabulary with AI assistance',
};

export default async function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<meta
					name="viewport"
					content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
				/>
				<link rel="manifest" href="/manifest.json" />
			</head>
			<body
				className={`${geist.variable} ${geistMono.variable} min-h-screen flex flex-col`}
				role="application"
				aria-label="Vocab Learning Application"
				aria-live="polite"
				suppressHydrationWarning
			>
				<ErrorManagementProvider
					config={{
						enableErrorReporting: true,
						enableNetworkDetection: true,
						enableApiInterception: true,
						environment: process.env.NODE_ENV,
						buildVersion: process.env.NEXT_PUBLIC_BUILD_VERSION,
					}}
				>
					<ThemeProvider>
						<TranslationProvider>
							<LoadingProvider>
								<AuthProvider>
									<FloatingUIProvider>
										<main className="flex-grow px-3 sm:px-9 py-3 sm:py-8">
											<div className="container mx-auto">
												<div className="mb-6 flex flex-col items-center justify-center sm:p-8 md:p-24 max-w-4xl mx-auto">
													{children}
												</div>
											</div>
										</main>
										<ProgressBar />
										<Toaster />
										<ClientSettings />
									</FloatingUIProvider>
								</AuthProvider>
							</LoadingProvider>
						</TranslationProvider>
					</ThemeProvider>
				</ErrorManagementProvider>
			</body>
		</html>
	);
}
