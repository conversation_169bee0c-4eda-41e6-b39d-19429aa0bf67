'use client';

import { Button } from '@/components/ui';
import { PageGuidance } from '@/components/onboarding/page-guidance';
import { useFloatingPosition } from '@/hooks/use-floating-position';
import { useState } from 'react';
import { Plus } from 'lucide-react';

export default function FloatingUITestPage() {
	const [showGuidance1, setShowGuidance1] = useState(true);
	const [showGuidance2, setShowGuidance2] = useState(true);
	const [showCustomButton, setShowCustomButton] = useState(true);

	// Custom floating button
	const { styles: customStyles } = useFloatingPosition('custom');

	return (
		<div className="min-h-screen p-8">
			<div className="max-w-4xl mx-auto space-y-8">
				<header className="text-center space-y-4">
					<h1 className="text-4xl font-bold">Floating UI Test Page</h1>
					<p className="text-muted-foreground">
						Test page để kiểm tra floating UI management system
					</p>
				</header>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div className="space-y-4">
						<h2 className="text-2xl font-semibold">Controls</h2>

						<div className="space-y-2">
							<Button
								onClick={() => setShowGuidance1(!showGuidance1)}
								variant={showGuidance1 ? 'default' : 'outline'}
							>
								{showGuidance1 ? 'Hide' : 'Show'} Page Guidance 1
							</Button>

							<Button
								onClick={() => setShowGuidance2(!showGuidance2)}
								variant={showGuidance2 ? 'default' : 'outline'}
							>
								{showGuidance2 ? 'Hide' : 'Show'} Page Guidance 2
							</Button>

							<Button
								onClick={() => setShowCustomButton(!showCustomButton)}
								variant={showCustomButton ? 'default' : 'outline'}
							>
								{showCustomButton ? 'Hide' : 'Show'} Custom Button
							</Button>
						</div>
					</div>

					<div className="space-y-4">
						<h2 className="text-2xl font-semibold">Expected Behavior</h2>
						<ul className="space-y-2 text-sm text-muted-foreground">
							<li>• Floating elements should not overlap</li>
							<li>• Higher priority elements get better positions</li>
							<li>• Elements stack vertically when positions conflict</li>
							<li>• Z-index is automatically managed</li>
							<li>• Settings button should always be visible (highest priority)</li>
						</ul>
					</div>
				</div>

				<div className="space-y-6">
					<h2 className="text-2xl font-semibold">Page Content</h2>
					<p>
						Đây là nội dung trang để test. Các floating elements sẽ xuất hiện ở góc dưới
						bên phải và không được đè lên nhau.
					</p>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						{Array.from({ length: 6 }, (_, i) => (
							<div key={i} className="p-4 border rounded-lg">
								<h3 className="font-semibold mb-2">Card {i + 1}</h3>
								<p className="text-sm text-muted-foreground">
									Lorem ipsum dolor sit amet, consectetur adipiscing elit.
								</p>
							</div>
						))}
					</div>
				</div>

				{/* Page Guidance Components */}
				{showGuidance1 && (
					<PageGuidance
						id="test-guidance-1"
						titleKey="Test Guidance 1"
						steps={[
							{ key: 'Step 1: This is the first guidance' },
							{ key: 'Step 2: This is the second step' },
							{ key: 'Step 3: This is the final step' },
						]}
						tipKey="This is a helpful tip for guidance 1"
						defaultOpen={false}
					/>
				)}

				{showGuidance2 && (
					<PageGuidance
						id="test-guidance-2"
						titleKey="Test Guidance 2"
						steps={[
							{ key: 'Step 1: Another guidance component' },
							{ key: 'Step 2: With different content' },
						]}
						tipKey="This is a tip for guidance 2"
						defaultOpen={false}
					/>
				)}

				{/* Custom floating button */}
				{showCustomButton && (
					<Button
						size="icon"
						className="h-12 w-12 rounded-full shadow-lg bg-green-600 hover:bg-green-700 text-white"
						style={customStyles}
						onClick={() => alert('Custom button clicked!')}
					>
						<Plus className="h-5 w-5" />
					</Button>
				)}
			</div>
		</div>
	);
}
