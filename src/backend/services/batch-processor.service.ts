import { Language, Difficulty } from '@prisma/client';

export interface BatchRequest<T = any> {
	id: string;
	operation: string;
	params: any;
	priority: 'high' | 'normal' | 'low';
	timestamp: number;
	resolve: (value: T) => void;
	reject: (error: Error) => void;
	timeout?: number;
	userId?: string;
}

export interface BatchGroup {
	operation: string;
	requests: BatchRequest[];
	estimatedTokens: number;
	priority: 'high' | 'normal' | 'low';
}

export interface BatchResult<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	requestId: string;
	processingTime: number;
}

export interface BatchProcessingConfig {
	maxBatchSize: Record<string, number>;
	maxWaitTime: number; // milliseconds
	maxTokensPerBatch: number;
	priorityWeights: Record<string, number>;
}

export class BatchProcessorService {
	private queue: BatchRequest[] = [];
	private processing = false;
	private processingTimer: NodeJS.Timeout | null = null;

	private readonly config: BatchProcessingConfig = {
		maxBatchSize: {
			generateWordDetails: 20,
			generateRandomTerms: 1, // Already optimized for single calls
			evaluateAnswers: 10,
			generateQuestions: 5,
			generateParagraph: 3,
			evaluateTranslation: 15,
			generateGrammarPractice: 2,
		},
		maxWaitTime: 2000, // 2 seconds max wait
		maxTokensPerBatch: 8000, // Stay under model limits
		priorityWeights: {
			high: 3,
			normal: 2,
			low: 1,
		},
	};

	/**
	 * Add request to batch queue
	 */
	async addToBatch<T>(
		operation: string,
		params: any,
		priority: 'high' | 'normal' | 'low' = 'normal',
		timeout: number = 30000,
		userId?: string
	): Promise<T> {
		return new Promise((resolve, reject) => {
			const request: BatchRequest<T> = {
				id: this.generateRequestId(),
				operation,
				params,
				priority,
				timestamp: Date.now(),
				resolve,
				reject,
				timeout,
				userId,
			};

			this.queue.push(request);

			// Set timeout for individual request
			setTimeout(() => {
				const index = this.queue.findIndex((r) => r.id === request.id);
				if (index !== -1) {
					this.queue.splice(index, 1);
					reject(new Error(`Request ${request.id} timed out`));
				}
			}, timeout);

			this.scheduleProcessing();
		});
	}

	/**
	 * Schedule batch processing
	 */
	private scheduleProcessing(): void {
		if (this.processing) return;

		// Clear existing timer
		if (this.processingTimer) {
			clearTimeout(this.processingTimer);
		}

		// Process immediately if high priority requests or queue is full
		const hasHighPriority = this.queue.some((r) => r.priority === 'high');
		const queueFull = this.queue.length >= this.getMaxQueueSize();

		if (hasHighPriority || queueFull) {
			this.processBatch();
		} else {
			// Wait for more requests or timeout
			this.processingTimer = setTimeout(() => {
				this.processBatch();
			}, this.config.maxWaitTime);
		}
	}

	/**
	 * Process batch of requests
	 */
	private async processBatch(): Promise<void> {
		if (this.processing || this.queue.length === 0) return;

		this.processing = true;

		try {
			// Clear processing timer
			if (this.processingTimer) {
				clearTimeout(this.processingTimer);
				this.processingTimer = null;
			}

			// Group requests by operation and priority
			const groups = this.groupRequests();

			// Process each group
			for (const group of groups) {
				await this.processGroup(group);
			}
		} catch (error) {
			console.error('Batch processing error:', error);
		} finally {
			this.processing = false;

			// Schedule next batch if queue not empty
			if (this.queue.length > 0) {
				this.scheduleProcessing();
			}
		}
	}

	/**
	 * Group requests by operation and priority
	 */
	private groupRequests(): BatchGroup[] {
		const groups: Map<string, BatchRequest[]> = new Map();

		// Sort by priority and timestamp
		const sortedQueue = [...this.queue].sort((a, b) => {
			const priorityDiff =
				this.config.priorityWeights[b.priority] - this.config.priorityWeights[a.priority];
			if (priorityDiff !== 0) return priorityDiff;
			return a.timestamp - b.timestamp;
		});

		// Group by operation
		for (const request of sortedQueue) {
			const key = request.operation;
			if (!groups.has(key)) {
				groups.set(key, []);
			}
			groups.get(key)!.push(request);
		}

		// Convert to BatchGroup array
		const result: BatchGroup[] = [];
		for (const [operation, requests] of groups) {
			const maxSize = this.config.maxBatchSize[operation] || 1;
			const chunks = this.chunkRequests(requests, maxSize);

			for (const chunk of chunks) {
				result.push({
					operation,
					requests: chunk,
					estimatedTokens: this.estimateGroupTokens(operation, chunk),
					priority: this.getGroupPriority(chunk),
				});
			}
		}

		return result.sort(
			(a, b) =>
				this.config.priorityWeights[b.priority] - this.config.priorityWeights[a.priority]
		);
	}

	/**
	 * Process a group of requests
	 */
	private async processGroup(group: BatchGroup): Promise<void> {
		const startTime = Date.now();

		try {
			// Remove requests from queue
			for (const request of group.requests) {
				const index = this.queue.findIndex((r) => r.id === request.id);
				if (index !== -1) {
					this.queue.splice(index, 1);
				}
			}

			// Process based on operation type
			let results: BatchResult[];

			if (this.isBatchableOperation(group.operation)) {
				results = await this.processBatchableGroup(group);
			} else {
				results = await this.processIndividualGroup(group);
			}

			// Resolve individual requests
			for (let i = 0; i < group.requests.length; i++) {
				const request = group.requests[i];
				const result = results[i];

				if (result.success) {
					request.resolve(result.data);
				} else {
					request.reject(new Error(result.error || 'Batch processing failed'));
				}
			}
		} catch (error) {
			// Reject all requests in group
			const processingTime = Date.now() - startTime;
			for (const request of group.requests) {
				request.reject(error as Error);
			}

			console.error(`Batch group processing failed for ${group.operation}:`, error);
		}
	}

	/**
	 * Process batchable operations (can combine multiple requests)
	 */
	private async processBatchableGroup(group: BatchGroup): Promise<BatchResult[]> {
		const { operation, requests } = group;

		switch (operation) {
			case 'generateWordDetails':
				return this.batchGenerateWordDetails(requests);

			case 'evaluateAnswers':
				return this.batchEvaluateAnswers(requests);

			case 'evaluateTranslation':
				return this.batchEvaluateTranslation(requests);

			default:
				return this.processIndividualGroup(group);
		}
	}

	/**
	 * Process non-batchable operations individually
	 */
	private async processIndividualGroup(group: BatchGroup): Promise<BatchResult[]> {
		const results: BatchResult[] = [];

		for (const request of group.requests) {
			const startTime = Date.now();
			try {
				// This would call the actual LLM service method
				const data = await this.callLLMService(request.operation, request.params);

				results.push({
					success: true,
					data,
					requestId: request.id,
					processingTime: Date.now() - startTime,
				});
			} catch (error) {
				results.push({
					success: false,
					error: error instanceof Error ? error.message : 'Unknown error',
					requestId: request.id,
					processingTime: Date.now() - startTime,
				});
			}
		}

		return results;
	}

	/**
	 * Batch generate word details
	 */
	private async batchGenerateWordDetails(requests: BatchRequest[]): Promise<BatchResult[]> {
		// Combine all terms from requests
		const allTerms: string[] = [];
		const requestTermsMap: Map<string, string[]> = new Map();

		for (const request of requests) {
			const terms = request.params.terms || [];
			allTerms.push(...terms);
			requestTermsMap.set(request.id, terms);
		}

		// Remove duplicates
		const uniqueTerms = [...new Set(allTerms)];

		try {
			// Call LLM service with combined terms
			const batchResult = await this.callLLMService('generateWordDetails', {
				terms: uniqueTerms,
				source_language: requests[0].params.source_language,
				target_language: requests[0].params.target_language,
			});

			// Split results back to individual requests
			const results: BatchResult[] = [];

			for (const request of requests) {
				const requestTerms = requestTermsMap.get(request.id) || [];
				const requestResults = batchResult.filter((word: any) =>
					requestTerms.includes(word.term)
				);

				results.push({
					success: true,
					data: requestResults,
					requestId: request.id,
					processingTime: 0, // Will be set by caller
				});
			}

			return results;
		} catch (error) {
			// Return error for all requests
			return requests.map((request) => ({
				success: false,
				error: error instanceof Error ? error.message : 'Batch processing failed',
				requestId: request.id,
				processingTime: 0,
			}));
		}
	}

	/**
	 * Batch evaluate answers
	 */
	private async batchEvaluateAnswers(requests: BatchRequest[]): Promise<BatchResult[]> {
		// For now, process individually as combining evaluations is complex
		return this.processIndividualGroup({
			operation: 'evaluateAnswers',
			requests,
			estimatedTokens: 0,
			priority: 'normal',
		});
	}

	/**
	 * Batch evaluate translations
	 */
	private async batchEvaluateTranslation(requests: BatchRequest[]): Promise<BatchResult[]> {
		// Combine multiple translation evaluations into single prompt
		const evaluations = requests.map((req) => ({
			original: req.params.original,
			translation: req.params.translation,
			requestId: req.id,
		}));

		try {
			const batchResult = await this.callLLMService('batchEvaluateTranslations', {
				evaluations,
				source_language: requests[0].params.source_language,
				target_language: requests[0].params.target_language,
			});

			// Map results back to requests
			return requests.map((request, index) => ({
				success: true,
				data: batchResult[index],
				requestId: request.id,
				processingTime: 0,
			}));
		} catch (error) {
			return requests.map((request) => ({
				success: false,
				error: error instanceof Error ? error.message : 'Batch evaluation failed',
				requestId: request.id,
				processingTime: 0,
			}));
		}
	}

	/**
	 * Helper methods
	 */
	private generateRequestId(): string {
		return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	private chunkRequests(requests: BatchRequest[], maxSize: number): BatchRequest[][] {
		const chunks: BatchRequest[][] = [];
		for (let i = 0; i < requests.length; i += maxSize) {
			chunks.push(requests.slice(i, i + maxSize));
		}
		return chunks;
	}

	private estimateGroupTokens(operation: string, requests: BatchRequest[]): number {
		// Simplified token estimation for batch
		const baseTokens: Record<string, number> = {
			generateWordDetails: 100,
			evaluateAnswers: 200,
			generateParagraph: 300,
			evaluateTranslation: 150,
		};

		return (baseTokens[operation] || 100) * requests.length;
	}

	private getGroupPriority(requests: BatchRequest[]): 'high' | 'normal' | 'low' {
		// Return highest priority in group
		if (requests.some((r) => r.priority === 'high')) return 'high';
		if (requests.some((r) => r.priority === 'normal')) return 'normal';
		return 'low';
	}

	private getMaxQueueSize(): number {
		return Math.max(...Object.values(this.config.maxBatchSize));
	}

	private isBatchableOperation(operation: string): boolean {
		return ['generateWordDetails', 'evaluateTranslation'].includes(operation);
	}

	private async callLLMService(operation: string, params: any): Promise<any> {
		// This would be injected or imported
		// For now, throw error to indicate implementation needed
		throw new Error(`LLM service call not implemented for ${operation}`);
	}

	/**
	 * Get batch processing statistics
	 */
	getStats() {
		return {
			queueLength: this.queue.length,
			processing: this.processing,
			config: this.config,
		};
	}

	/**
	 * Clear queue (for testing/emergency)
	 */
	clearQueue(): void {
		for (const request of this.queue) {
			request.reject(new Error('Queue cleared'));
		}
		this.queue = [];
	}
}

// Singleton instance
export const batchProcessor = new BatchProcessorService();
