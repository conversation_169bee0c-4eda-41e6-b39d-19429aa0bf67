import type { Difficulty, Language, Paragraph, Prisma, PrismaClient } from '@prisma/client';
import { type BaseRepository, BaseRepositoryImpl } from './base.repository';

// Define the include structure for Paragraph queries
export const paragraphInclude = {
	multiple_choice_exercises: true,
};

export interface ParagraphRepository extends BaseRepository<Paragraph> {
	findByLanguage(language: Language, limit?: number): Promise<Paragraph[]>;
	findByDifficulty(difficulty: Difficulty, limit?: number): Promise<Paragraph[]>;
	findByIds(ids: string[]): Promise<Paragraph[]>;
	createMany(data: Prisma.ParagraphCreateInput[]): Promise<Paragraph[]>;
	findWithExercises(id: string): Promise<Paragraph | null>;
}

export class ParagraphRepositoryImpl extends BaseRepositoryImpl<Paragraph> implements ParagraphRepository {
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.paragraph);
	}

	override async findById(id: string): Promise<Paragraph | null> {
		const paragraph = await this.prisma.paragraph.findUnique({
			where: { id },
			include: paragraphInclude,
		});
		return paragraph;
	}

	override async findOne(query: Record<string, unknown>): Promise<Paragraph | null> {
		const paragraph = await this.prisma.paragraph.findFirst({
			where: query,
			include: paragraphInclude,
		});
		return paragraph;
	}

	override async find(query: Prisma.ParagraphWhereInput, limit?: number): Promise<Paragraph[]> {
		const paragraphs = await this.prisma.paragraph.findMany({
			where: query,
			include: paragraphInclude,
			take: limit,
		});
		return paragraphs;
	}

	override async create(data: Prisma.ParagraphCreateInput): Promise<Paragraph> {
		if (!data.content || !data.language || !data.difficulty || !data.length) {
			throw new Error('Content, language, difficulty, and length are required');
		}

		const paragraph = await this.prisma.paragraph.create({
			data,
			include: paragraphInclude,
		});
		return paragraph;
	}

	override async update(id: string, data: Prisma.ParagraphUpdateInput): Promise<Paragraph> {
		const paragraph = await this.prisma.paragraph.update({
			where: { id },
			data,
			include: paragraphInclude,
		});
		return paragraph;
	}

	override async delete(query: Record<string, unknown>): Promise<void> {
		await this.prisma.paragraph.deleteMany({
			where: query,
		});
	}

	async findByLanguage(language: Language, limit?: number): Promise<Paragraph[]> {
		return this.prisma.paragraph.findMany({
			where: { language },
			include: paragraphInclude,
			take: limit,
		});
	}

	async findByDifficulty(difficulty: Difficulty, limit?: number): Promise<Paragraph[]> {
		return this.prisma.paragraph.findMany({
			where: { difficulty },
			include: paragraphInclude,
			take: limit,
		});
	}

	async findByIds(ids: string[]): Promise<Paragraph[]> {
		return this.prisma.paragraph.findMany({
			where: {
				id: {
					in: ids,
				},
			},
			include: paragraphInclude,
		});
	}

	async createMany(data: Prisma.ParagraphCreateInput[]): Promise<Paragraph[]> {
		const results: Paragraph[] = [];
		
		for (const item of data) {
			const paragraph = await this.create(item);
			results.push(paragraph);
		}
		
		return results;
	}

	async findWithExercises(id: string): Promise<Paragraph | null> {
		return this.prisma.paragraph.findUnique({
			where: { id },
			include: {
				multiple_choice_exercises: true,
			},
		});
	}
}
