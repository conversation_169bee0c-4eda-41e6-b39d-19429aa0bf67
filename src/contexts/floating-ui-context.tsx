'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';

export interface FloatingElement {
	id: string;
	type: 'page-guidance' | 'float-button' | 'settings' | 'info' | 'custom';
	priority: number; // Higher number = higher priority
	position: {
		bottom: number;
		right: number;
	};
	isVisible: boolean;
	zIndex: number;
}

interface FloatingUIContextType {
	elements: FloatingElement[];
	registerElement: (element: Omit<FloatingElement, 'zIndex'>) => void;
	unregisterElement: (id: string) => void;
	updateElement: (id: string, updates: Partial<FloatingElement>) => void;
	getElementPosition: (id: string) => { bottom: number; right: number; zIndex: number } | null;
	hideAllExcept: (id: string) => void;
	showAll: () => void;
}

const FloatingUIContext = createContext<FloatingUIContextType | undefined>(undefined);

// Default positions and priorities for different element types
const DEFAULT_CONFIGS = {
	'page-guidance': { priority: 1, position: { bottom: 16, right: 16 } }, // 4 * 4px = 16px
	'float-button': { priority: 2, position: { bottom: 24, right: 24 } }, // 6 * 4px = 24px
	'settings': { priority: 3, position: { bottom: 24, right: 24 } },
	'info': { priority: 4, position: { bottom: 96, right: 24 } }, // 24 * 4px = 96px (bottom-24)
	'custom': { priority: 5, position: { bottom: 24, right: 24 } },
};

const BASE_Z_INDEX = 50;
const Z_INDEX_STEP = 1;

export function FloatingUIProvider({ children }: { children: React.ReactNode }) {
	const [elements, setElements] = useState<FloatingElement[]>([]);

	// Calculate positions to avoid overlaps
	const calculatePositions = useCallback((elements: FloatingElement[]) => {
		// Sort by priority (higher priority gets better position)
		const sortedElements = [...elements].sort((a, b) => b.priority - a.priority);
		const updatedElements: FloatingElement[] = [];
		
		// Track occupied positions
		const occupiedPositions = new Set<string>();
		
		sortedElements.forEach((element, index) => {
			let position = { ...element.position };
			let zIndex = BASE_Z_INDEX + (sortedElements.length - index) * Z_INDEX_STEP;
			
			// If element is visible, check for position conflicts
			if (element.isVisible) {
				const positionKey = `${position.bottom}-${position.right}`;
				
				// If position is occupied, stack elements vertically
				if (occupiedPositions.has(positionKey)) {
					// Find a free position by moving up
					let offset = 0;
					let newPositionKey = positionKey;
					
					while (occupiedPositions.has(newPositionKey)) {
						offset += 80; // 80px spacing between stacked elements
						const newBottom = position.bottom + offset;
						newPositionKey = `${newBottom}-${position.right}`;
					}
					
					position.bottom += offset;
				}
				
				occupiedPositions.add(`${position.bottom}-${position.right}`);
			}
			
			updatedElements.push({
				...element,
				position,
				zIndex,
			});
		});
		
		return updatedElements;
	}, []);

	const registerElement = useCallback((element: Omit<FloatingElement, 'zIndex'>) => {
		setElements(prev => {
			// Remove existing element with same id
			const filtered = prev.filter(el => el.id !== element.id);
			
			// Add new element with default config if not provided
			const defaultConfig = DEFAULT_CONFIGS[element.type] || DEFAULT_CONFIGS.custom;
			const newElement: FloatingElement = {
				...element,
				priority: element.priority || defaultConfig.priority,
				position: element.position || defaultConfig.position,
				zIndex: BASE_Z_INDEX,
			};
			
			const newElements = [...filtered, newElement];
			return calculatePositions(newElements);
		});
	}, [calculatePositions]);

	const unregisterElement = useCallback((id: string) => {
		setElements(prev => {
			const filtered = prev.filter(el => el.id !== id);
			return calculatePositions(filtered);
		});
	}, [calculatePositions]);

	const updateElement = useCallback((id: string, updates: Partial<FloatingElement>) => {
		setElements(prev => {
			const updated = prev.map(el => 
				el.id === id ? { ...el, ...updates } : el
			);
			return calculatePositions(updated);
		});
	}, [calculatePositions]);

	const getElementPosition = useCallback((id: string) => {
		const element = elements.find(el => el.id === id);
		return element ? {
			bottom: element.position.bottom,
			right: element.position.right,
			zIndex: element.zIndex,
		} : null;
	}, [elements]);

	const hideAllExcept = useCallback((id: string) => {
		setElements(prev => {
			const updated = prev.map(el => ({
				...el,
				isVisible: el.id === id,
			}));
			return calculatePositions(updated);
		});
	}, [calculatePositions]);

	const showAll = useCallback(() => {
		setElements(prev => {
			const updated = prev.map(el => ({
				...el,
				isVisible: true,
			}));
			return calculatePositions(updated);
		});
	}, [calculatePositions]);

	// Recalculate positions when elements change
	useEffect(() => {
		setElements(prev => calculatePositions(prev));
	}, [calculatePositions]);

	const value: FloatingUIContextType = {
		elements,
		registerElement,
		unregisterElement,
		updateElement,
		getElementPosition,
		hideAllExcept,
		showAll,
	};

	return (
		<FloatingUIContext.Provider value={value}>
			{children}
		</FloatingUIContext.Provider>
	);
}

export function useFloatingUI() {
	const context = useContext(FloatingUIContext);
	if (context === undefined) {
		throw new Error('useFloatingUI must be used within a FloatingUIProvider');
	}
	return context;
}

// Hook for individual floating elements
export function useFloatingElement(
	id: string,
	type: FloatingElement['type'],
	options?: {
		priority?: number;
		position?: { bottom: number; right: number };
		isVisible?: boolean;
	}
) {
	const { registerElement, unregisterElement, updateElement, getElementPosition } = useFloatingUI();

	// Register element on mount
	useEffect(() => {
		registerElement({
			id,
			type,
			priority: options?.priority || DEFAULT_CONFIGS[type]?.priority || DEFAULT_CONFIGS.custom.priority,
			position: options?.position || DEFAULT_CONFIGS[type]?.position || DEFAULT_CONFIGS.custom.position,
			isVisible: options?.isVisible ?? true,
		});

		return () => {
			unregisterElement(id);
		};
	}, [id, type, registerElement, unregisterElement, options?.priority, options?.position, options?.isVisible]);

	// Update visibility
	const setVisible = useCallback((isVisible: boolean) => {
		updateElement(id, { isVisible });
	}, [id, updateElement]);

	// Update position
	const setPosition = useCallback((position: { bottom: number; right: number }) => {
		updateElement(id, { position });
	}, [id, updateElement]);

	// Update priority
	const setPriority = useCallback((priority: number) => {
		updateElement(id, { priority });
	}, [id, updateElement]);

	const position = getElementPosition(id);

	return {
		position,
		setVisible,
		setPosition,
		setPriority,
	};
}
